<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">92%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-22 12:25 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_227eabb787ae78a2___init___py.html">src/pyrt_dicom/__init__.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_227eabb787ae78a2___main___py.html">src/pyrt_dicom/__main__.py</a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_227eabb787ae78a2_cli_py.html">src/pyrt_dicom/cli.py</a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6___init___py.html">src/pyrt_dicom/coordinates/__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html">src/pyrt_dicom/coordinates/reference_frames.py</a></td>
                <td>118</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="106 118">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_transforms_py.html">src/pyrt_dicom/coordinates/transforms.py</a></td>
                <td>107</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="101 107">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9ff87e177b53296___init___py.html">src/pyrt_dicom/core/__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html">src/pyrt_dicom/core/base.py</a></td>
                <td>152</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="146 152">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_227eabb787ae78a2_pyrt_dicom_py.html">src/pyrt_dicom/pyrt_dicom.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72___init___py.html">src/pyrt_dicom/uid_generation/__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html">src/pyrt_dicom/uid_generation/generators.py</a></td>
                <td>78</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="75 78">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html">src/pyrt_dicom/uid_generation/registry.py</a></td>
                <td>121</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="110 121">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_227eabb787ae78a2_utils_py.html">src/pyrt_dicom/utils.py</a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d4eef1efddc0da58___init___py.html">src/pyrt_dicom/utils/__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d4eef1efddc0da58_exceptions_py.html">src/pyrt_dicom/utils/exceptions.py</a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d4eef1efddc0da58_logging_py.html">src/pyrt_dicom/utils/logging.py</a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4060536b24be2b90___init___py.html">src/pyrt_dicom/validation/__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4060536b24be2b90_geometric_py.html">src/pyrt_dicom/validation/geometric.py</a></td>
                <td>119</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="112 119">94%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>772</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="710 772">92%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-22 12:25 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_4060536b24be2b90_geometric_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_227eabb787ae78a2___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
