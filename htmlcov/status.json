{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.2", "globals": "de91198ad93102f1c071962518e520fc", "files": {"z_227eabb787ae78a2___init___py": {"hash": "a967429c6ee6efac497811d0d0fef9cf", "index": {"url": "z_227eabb787ae78a2___init___py.html", "file": "src/pyrt_dicom/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_227eabb787ae78a2___main___py": {"hash": "460bfcfc17b60078558f9f54aa18af56", "index": {"url": "z_227eabb787ae78a2___main___py.html", "file": "src/pyrt_dicom/__main__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_227eabb787ae78a2_cli_py": {"hash": "1d23783da6f771f1888faac8dd9855b2", "index": {"url": "z_227eabb787ae78a2_cli_py.html", "file": "src/pyrt_dicom/cli.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 12, "n_excluded": 0, "n_missing": 12, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0d9011022451dbf6___init___py": {"hash": "a662b4a5d5f986f87452067c8ec3ed26", "index": {"url": "z_0d9011022451dbf6___init___py.html", "file": "src/pyrt_dicom/coordinates/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0d9011022451dbf6_reference_frames_py": {"hash": "03a50520c0d8b3b664e666ecaf251405", "index": {"url": "z_0d9011022451dbf6_reference_frames_py.html", "file": "src/pyrt_dicom/coordinates/reference_frames.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 118, "n_excluded": 0, "n_missing": 12, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0d9011022451dbf6_transforms_py": {"hash": "a00366d0d1a75e671129b4a4c7d52b42", "index": {"url": "z_0d9011022451dbf6_transforms_py.html", "file": "src/pyrt_dicom/coordinates/transforms.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 107, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b9ff87e177b53296___init___py": {"hash": "de0d921b9ddd8e3d30a43207e3de3fd0", "index": {"url": "z_b9ff87e177b53296___init___py.html", "file": "src/pyrt_dicom/core/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b9ff87e177b53296_base_py": {"hash": "a4bfceafe70f387100becefa9f2e7c4d", "index": {"url": "z_b9ff87e177b53296_base_py.html", "file": "src/pyrt_dicom/core/base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 152, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_227eabb787ae78a2_pyrt_dicom_py": {"hash": "ea4b853969862592d461ab3b8212baee", "index": {"url": "z_227eabb787ae78a2_pyrt_dicom_py.html", "file": "src/pyrt_dicom/pyrt_dicom.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c5a519e243f29b72___init___py": {"hash": "94dd9e0cfcf90904a167f6577aaff13d", "index": {"url": "z_c5a519e243f29b72___init___py.html", "file": "src/pyrt_dicom/uid_generation/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c5a519e243f29b72_generators_py": {"hash": "01ee4bd6f01cb3ca529188fc21ab8447", "index": {"url": "z_c5a519e243f29b72_generators_py.html", "file": "src/pyrt_dicom/uid_generation/generators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 78, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c5a519e243f29b72_registry_py": {"hash": "6ef4e70c2158c962d6b14466211671df", "index": {"url": "z_c5a519e243f29b72_registry_py.html", "file": "src/pyrt_dicom/uid_generation/registry.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 121, "n_excluded": 0, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_227eabb787ae78a2_utils_py": {"hash": "1e7203289ab7dac9546e0b1efca50083", "index": {"url": "z_227eabb787ae78a2_utils_py.html", "file": "src/pyrt_dicom/utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d4eef1efddc0da58___init___py": {"hash": "acbf47c804e351cf5dba3be49e0df9cb", "index": {"url": "z_d4eef1efddc0da58___init___py.html", "file": "src/pyrt_dicom/utils/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d4eef1efddc0da58_exceptions_py": {"hash": "8548340d9ec896e7df1549e0c7b32c01", "index": {"url": "z_d4eef1efddc0da58_exceptions_py.html", "file": "src/pyrt_dicom/utils/exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 12, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d4eef1efddc0da58_logging_py": {"hash": "e0cf09c2289d3a5c71e1efa805f40e03", "index": {"url": "z_d4eef1efddc0da58_logging_py.html", "file": "src/pyrt_dicom/utils/logging.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 29, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4060536b24be2b90___init___py": {"hash": "b707875ad90471234fa322a9a9ae9667", "index": {"url": "z_4060536b24be2b90___init___py.html", "file": "src/pyrt_dicom/validation/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4060536b24be2b90_geometric_py": {"hash": "49fb9ebe05ec8800b1368840f45d3c9b", "index": {"url": "z_4060536b24be2b90_geometric_py.html", "file": "src/pyrt_dicom/validation/geometric.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 119, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}