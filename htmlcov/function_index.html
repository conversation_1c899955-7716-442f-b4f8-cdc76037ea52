<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">92%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-22 12:25 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_227eabb787ae78a2___init___py.html">src/pyrt_dicom/__init__.py</a></td>
                <td class="name left"><a href="z_227eabb787ae78a2___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_227eabb787ae78a2___main___py.html">src/pyrt_dicom/__main__.py</a></td>
                <td class="name left"><a href="z_227eabb787ae78a2___main___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_227eabb787ae78a2_cli_py.html#t13">src/pyrt_dicom/cli.py</a></td>
                <td class="name left"><a href="z_227eabb787ae78a2_cli_py.html#t13"><data value='main'>main</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_227eabb787ae78a2_cli_py.html">src/pyrt_dicom/cli.py</a></td>
                <td class="name left"><a href="z_227eabb787ae78a2_cli_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6___init___py.html">src/pyrt_dicom/coordinates/__init__.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t31">src/pyrt_dicom/coordinates/reference_frames.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t31"><data value='post_init__'>GeometricParameters.__post_init__</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t71">src/pyrt_dicom/coordinates/reference_frames.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t71"><data value='add_associated_object'>FrameOfReferenceInfo.add_associated_object</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t75">src/pyrt_dicom/coordinates/reference_frames.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t75"><data value='get_associated_objects_by_type'>FrameOfReferenceInfo.get_associated_objects_by_type</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t93">src/pyrt_dicom/coordinates/reference_frames.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t93"><data value='init__'>FrameOfReference.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t106">src/pyrt_dicom/coordinates/reference_frames.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t106"><data value='create_frame_of_reference'>FrameOfReference.create_frame_of_reference</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t156">src/pyrt_dicom/coordinates/reference_frames.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t156"><data value='get_frame_of_reference'>FrameOfReference.get_frame_of_reference</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t181">src/pyrt_dicom/coordinates/reference_frames.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t181"><data value='associate_object'>FrameOfReference.associate_object</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t211">src/pyrt_dicom/coordinates/reference_frames.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t211"><data value='validate_geometric_consistency'>FrameOfReference.validate_geometric_consistency</data></a></td>
                <td>17</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="15 17">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t288">src/pyrt_dicom/coordinates/reference_frames.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t288"><data value='get_coordinate_transformer'>FrameOfReference.get_coordinate_transformer</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t322">src/pyrt_dicom/coordinates/reference_frames.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t322"><data value='get_primary_frame_uid'>FrameOfReference.get_primary_frame_uid</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t326">src/pyrt_dicom/coordinates/reference_frames.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t326"><data value='set_primary_frame_uid'>FrameOfReference.set_primary_frame_uid</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t346">src/pyrt_dicom/coordinates/reference_frames.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t346"><data value='list_frames'>FrameOfReference.list_frames</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t350">src/pyrt_dicom/coordinates/reference_frames.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t350"><data value='get_frame_summary'>FrameOfReference.get_frame_summary</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t392">src/pyrt_dicom/coordinates/reference_frames.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html#t392"><data value='validate_frame_consistency'>FrameOfReference.validate_frame_consistency</data></a></td>
                <td>19</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="11 19">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html">src/pyrt_dicom/coordinates/reference_frames.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_reference_frames_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>36</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="36 36">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_transforms_py.html#t66">src/pyrt_dicom/coordinates/transforms.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_transforms_py.html#t66"><data value='init__'>CoordinateTransformer.__init__</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_transforms_py.html#t109">src/pyrt_dicom/coordinates/transforms.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_transforms_py.html#t109"><data value='validate_patient_position'>CoordinateTransformer._validate_patient_position</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_transforms_py.html#t120">src/pyrt_dicom/coordinates/transforms.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_transforms_py.html#t120"><data value='validate_image_orientation'>CoordinateTransformer._validate_image_orientation</data></a></td>
                <td>18</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="16 18">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_transforms_py.html#t166">src/pyrt_dicom/coordinates/transforms.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_transforms_py.html#t166"><data value='dicom_to_patient'>CoordinateTransformer.dicom_to_patient</data></a></td>
                <td>16</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="15 16">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_transforms_py.html#t222">src/pyrt_dicom/coordinates/transforms.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_transforms_py.html#t222"><data value='patient_to_dicom'>CoordinateTransformer.patient_to_dicom</data></a></td>
                <td>18</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="16 18">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_transforms_py.html#t284">src/pyrt_dicom/coordinates/transforms.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_transforms_py.html#t284"><data value='get_transformation_matrix'>CoordinateTransformer.get_transformation_matrix</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_transforms_py.html#t313">src/pyrt_dicom/coordinates/transforms.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_transforms_py.html#t313"><data value='validate_patient_position'>validate_patient_position</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_transforms_py.html#t345">src/pyrt_dicom/coordinates/transforms.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_transforms_py.html#t345"><data value='transform_image_orientation'>transform_image_orientation</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_transforms_py.html#t381">src/pyrt_dicom/coordinates/transforms.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_transforms_py.html#t381"><data value='dicom_to_patient_coordinates'>dicom_to_patient_coordinates</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_transforms_py.html#t422">src/pyrt_dicom/coordinates/transforms.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_transforms_py.html#t422"><data value='patient_to_dicom_coordinates'>patient_to_dicom_coordinates</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0d9011022451dbf6_transforms_py.html">src/pyrt_dicom/coordinates/transforms.py</a></td>
                <td class="name left"><a href="z_0d9011022451dbf6_transforms_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9ff87e177b53296___init___py.html">src/pyrt_dicom/core/__init__.py</a></td>
                <td class="name left"><a href="z_b9ff87e177b53296___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t62">src/pyrt_dicom/core/base.py</a></td>
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t62"><data value='init__'>BaseDicomCreator.__init__</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t102">src/pyrt_dicom/core/base.py</a></td>
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t102"><data value='process_reference_image'>BaseDicomCreator._process_reference_image</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t135">src/pyrt_dicom/core/base.py</a></td>
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t135"><data value='create_base_dataset'>BaseDicomCreator._create_base_dataset</data></a></td>
                <td>36</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="36 36">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t213">src/pyrt_dicom/core/base.py</a></td>
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t213"><data value='set_patient_info'>BaseDicomCreator._set_patient_info</data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t251">src/pyrt_dicom/core/base.py</a></td>
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t251"><data value='set_study_info'>BaseDicomCreator._set_study_info</data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t296">src/pyrt_dicom/core/base.py</a></td>
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t296"><data value='create_modality_specific_dataset'>BaseDicomCreator._create_modality_specific_dataset</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t310">src/pyrt_dicom/core/base.py</a></td>
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t310"><data value='validate'>BaseDicomCreator.validate</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t346">src/pyrt_dicom/core/base.py</a></td>
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t346"><data value='validate_modality_specific'>BaseDicomCreator._validate_modality_specific</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t355">src/pyrt_dicom/core/base.py</a></td>
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t355"><data value='save'>BaseDicomCreator.save</data></a></td>
                <td>23</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="19 23">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t441">src/pyrt_dicom/core/base.py</a></td>
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t441"><data value='is_validated'>BaseDicomCreator.is_validated</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t446">src/pyrt_dicom/core/base.py</a></td>
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t446"><data value='validation_errors'>BaseDicomCreator.validation_errors</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t450">src/pyrt_dicom/core/base.py</a></td>
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html#t450"><data value='repr__'>BaseDicomCreator.__repr__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html">src/pyrt_dicom/core/base.py</a></td>
                <td class="name left"><a href="z_b9ff87e177b53296_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_227eabb787ae78a2_pyrt_dicom_py.html">src/pyrt_dicom/pyrt_dicom.py</a></td>
                <td class="name left"><a href="z_227eabb787ae78a2_pyrt_dicom_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72___init___py.html">src/pyrt_dicom/uid_generation/__init__.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t153">src/pyrt_dicom/uid_generation/generators.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t153"><data value='init__'>UIDGenerator.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t165">src/pyrt_dicom/uid_generation/generators.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t165"><data value='validate_root_uid'>UIDGenerator._validate_root_uid</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t190">src/pyrt_dicom/uid_generation/generators.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t190"><data value='generate_uid'>UIDGenerator.generate_uid</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t204">src/pyrt_dicom/uid_generation/generators.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t204"><data value='validate_generated_uid'>UIDGenerator._validate_generated_uid</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t223">src/pyrt_dicom/uid_generation/generators.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t223"><data value='generate_study_instance_uid'>UIDGenerator.generate_study_instance_uid</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t247">src/pyrt_dicom/uid_generation/generators.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t247"><data value='generate_series_instance_uid'>UIDGenerator.generate_series_instance_uid</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t271">src/pyrt_dicom/uid_generation/generators.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t271"><data value='generate_sop_instance_uid'>UIDGenerator.generate_sop_instance_uid</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t299">src/pyrt_dicom/uid_generation/generators.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t299"><data value='generate_frame_of_reference_uid'>UIDGenerator.generate_frame_of_reference_uid</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t413">src/pyrt_dicom/uid_generation/generators.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t413"><data value='generate_uid'>HashBasedUIDGenerator.generate_uid</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t579">src/pyrt_dicom/uid_generation/generators.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t579"><data value='generate_uid'>RandomUIDGenerator.generate_uid</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t668">src/pyrt_dicom/uid_generation/generators.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t668"><data value='create_hash_generator'>DefaultUIDGenerator.create_hash_generator</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t698">src/pyrt_dicom/uid_generation/generators.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t698"><data value='create_random_generator'>DefaultUIDGenerator.create_random_generator</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t728">src/pyrt_dicom/uid_generation/generators.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html#t728"><data value='create_default_generator'>DefaultUIDGenerator.create_default_generator</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html">src/pyrt_dicom/uid_generation/generators.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_generators_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t32">src/pyrt_dicom/uid_generation/registry.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t32"><data value='init__'>UIDRegistry.__init__</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t58">src/pyrt_dicom/uid_generation/registry.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t58"><data value='register_study_uid'>UIDRegistry.register_study_uid</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t77">src/pyrt_dicom/uid_generation/registry.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t77"><data value='register_series_uid'>UIDRegistry.register_series_uid</data></a></td>
                <td>10</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="9 10">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t113">src/pyrt_dicom/uid_generation/registry.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t113"><data value='register_instance_uid'>UIDRegistry.register_instance_uid</data></a></td>
                <td>10</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="8 10">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t149">src/pyrt_dicom/uid_generation/registry.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t149"><data value='register_frame_reference_uid'>UIDRegistry.register_frame_reference_uid</data></a></td>
                <td>9</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="7 9">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t184">src/pyrt_dicom/uid_generation/registry.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t184"><data value='create_and_register_study_uid'>UIDRegistry.create_and_register_study_uid</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t196">src/pyrt_dicom/uid_generation/registry.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t196"><data value='create_and_register_series_uid'>UIDRegistry.create_and_register_series_uid</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t210">src/pyrt_dicom/uid_generation/registry.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t210"><data value='create_and_register_instance_uid'>UIDRegistry.create_and_register_instance_uid</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t224">src/pyrt_dicom/uid_generation/registry.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t224"><data value='create_and_register_frame_reference_uid'>UIDRegistry.create_and_register_frame_reference_uid</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t238">src/pyrt_dicom/uid_generation/registry.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t238"><data value='get_study_series'>UIDRegistry.get_study_series</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t255">src/pyrt_dicom/uid_generation/registry.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t255"><data value='get_series_instances'>UIDRegistry.get_series_instances</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t272">src/pyrt_dicom/uid_generation/registry.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t272"><data value='get_study_frame_references'>UIDRegistry.get_study_frame_references</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t289">src/pyrt_dicom/uid_generation/registry.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t289"><data value='get_series_study'>UIDRegistry.get_series_study</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t306">src/pyrt_dicom/uid_generation/registry.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t306"><data value='get_instance_series'>UIDRegistry.get_instance_series</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t323">src/pyrt_dicom/uid_generation/registry.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t323"><data value='validate_hierarchy'>UIDRegistry.validate_hierarchy</data></a></td>
                <td>14</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="12 14">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t351">src/pyrt_dicom/uid_generation/registry.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t351"><data value='get_registry_summary'>UIDRegistry.get_registry_summary</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t365">src/pyrt_dicom/uid_generation/registry.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html#t365"><data value='clear'>UIDRegistry.clear</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html">src/pyrt_dicom/uid_generation/registry.py</a></td>
                <td class="name left"><a href="z_c5a519e243f29b72_registry_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_227eabb787ae78a2_utils_py.html#t1">src/pyrt_dicom/utils.py</a></td>
                <td class="name left"><a href="z_227eabb787ae78a2_utils_py.html#t1"><data value='do_something_useful'>do_something_useful</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_227eabb787ae78a2_utils_py.html">src/pyrt_dicom/utils.py</a></td>
                <td class="name left"><a href="z_227eabb787ae78a2_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d4eef1efddc0da58___init___py.html">src/pyrt_dicom/utils/__init__.py</a></td>
                <td class="name left"><a href="z_d4eef1efddc0da58___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d4eef1efddc0da58_exceptions_py.html">src/pyrt_dicom/utils/exceptions.py</a></td>
                <td class="name left"><a href="z_d4eef1efddc0da58_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d4eef1efddc0da58_logging_py.html#t95">src/pyrt_dicom/utils/logging.py</a></td>
                <td class="name left"><a href="z_d4eef1efddc0da58_logging_py.html#t95"><data value='format'>ClinicalFormatter.format</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d4eef1efddc0da58_logging_py.html#t123">src/pyrt_dicom/utils/logging.py</a></td>
                <td class="name left"><a href="z_d4eef1efddc0da58_logging_py.html#t123"><data value='get_clinical_logger'>get_clinical_logger</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d4eef1efddc0da58_logging_py.html#t148">src/pyrt_dicom/utils/logging.py</a></td>
                <td class="name left"><a href="z_d4eef1efddc0da58_logging_py.html#t148"><data value='log_dicom_creation'>log_dicom_creation</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d4eef1efddc0da58_logging_py.html#t174">src/pyrt_dicom/utils/logging.py</a></td>
                <td class="name left"><a href="z_d4eef1efddc0da58_logging_py.html#t174"><data value='log_validation_result'>log_validation_result</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d4eef1efddc0da58_logging_py.html">src/pyrt_dicom/utils/logging.py</a></td>
                <td class="name left"><a href="z_d4eef1efddc0da58_logging_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4060536b24be2b90___init___py.html">src/pyrt_dicom/validation/__init__.py</a></td>
                <td class="name left"><a href="z_4060536b24be2b90___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4060536b24be2b90_geometric_py.html#t43">src/pyrt_dicom/validation/geometric.py</a></td>
                <td class="name left"><a href="z_4060536b24be2b90_geometric_py.html#t43"><data value='init__'>GeometricValidator.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4060536b24be2b90_geometric_py.html#t63">src/pyrt_dicom/validation/geometric.py</a></td>
                <td class="name left"><a href="z_4060536b24be2b90_geometric_py.html#t63"><data value='validate_geometric_parameters'>GeometricValidator.validate_geometric_parameters</data></a></td>
                <td>16</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="15 16">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4060536b24be2b90_geometric_py.html#t123">src/pyrt_dicom/validation/geometric.py</a></td>
                <td class="name left"><a href="z_4060536b24be2b90_geometric_py.html#t123"><data value='validate_coordinate_consistency'>GeometricValidator.validate_coordinate_consistency</data></a></td>
                <td>18</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="16 18">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4060536b24be2b90_geometric_py.html#t189">src/pyrt_dicom/validation/geometric.py</a></td>
                <td class="name left"><a href="z_4060536b24be2b90_geometric_py.html#t189"><data value='validate_structure_bounds'>GeometricValidator.validate_structure_bounds</data></a></td>
                <td>26</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4060536b24be2b90_geometric_py.html#t276">src/pyrt_dicom/validation/geometric.py</a></td>
                <td class="name left"><a href="z_4060536b24be2b90_geometric_py.html#t276"><data value='validate_coordinate_bounds'>validate_coordinate_bounds</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4060536b24be2b90_geometric_py.html#t299">src/pyrt_dicom/validation/geometric.py</a></td>
                <td class="name left"><a href="z_4060536b24be2b90_geometric_py.html#t299"><data value='validate_geometric_consistency'>validate_geometric_consistency</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4060536b24be2b90_geometric_py.html#t337">src/pyrt_dicom/validation/geometric.py</a></td>
                <td class="name left"><a href="z_4060536b24be2b90_geometric_py.html#t337"><data value='validate_structure_geometry'>validate_structure_geometry</data></a></td>
                <td>20</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="16 20">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4060536b24be2b90_geometric_py.html#t404">src/pyrt_dicom/validation/geometric.py</a></td>
                <td class="name left"><a href="z_4060536b24be2b90_geometric_py.html#t404"><data value='check_contour_closure'>check_contour_closure</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4060536b24be2b90_geometric_py.html">src/pyrt_dicom/validation/geometric.py</a></td>
                <td class="name left"><a href="z_4060536b24be2b90_geometric_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>772</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="710 772">92%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-22 12:25 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
