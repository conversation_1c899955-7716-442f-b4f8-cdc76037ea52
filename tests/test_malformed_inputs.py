"""
Test malformed input robustness for pyrt-dicom.

This module tests the library's handling of malformed, corrupted, or invalid
inputs to ensure graceful failure with helpful error messages and recovery
guidance. Tests include:
- Corrupted DICOM reference images
- Incomplete patient information scenarios
- Coordinate system inconsistencies and recovery
- Invalid parameter combinations

These tests ensure the library fails gracefully and provides actionable
guidance for medical physicists when encountering problematic data.
"""

import pytest
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import tempfile
import pydicom
from pydicom.dataset import Dataset

from pyrt_dicom.core.base import BaseDicomCreator
from pyrt_dicom.validation.geometric import (
    GeometricValidator,
    validate_coordinate_bounds,
    validate_structure_geometry,
)
from pyrt_dicom.validation.patient import (
    PatientInfoValidator,
    validate_patient_info,
)
from pyrt_dicom.validation.dicom_tags import (
    DicomTagValidator,
    validate_dicom_tag_value,
)
from pyrt_dicom.coordinates.transforms import CoordinateTransformer
from pyrt_dicom.coordinates.reference_frames import (
    FrameOfReference,
    GeometricParameters,
)
from pyrt_dicom.utils.exceptions import (
    ValidationError,
    CoordinateSystemError,
    DicomCreationError,
    UIDGenerationError,
    TemplateError,
)


class TestBaseDicomCreator(BaseDicomCreator):
    """Concrete implementation for testing malformed inputs."""
    
    def _create_modality_specific_dataset(self):
        """Create minimal test dataset."""
        dataset = self._create_base_dataset()
        dataset.SOPClassUID = "1.2.840.10008.5.1.4.1.1.2"  # CT Image Storage
        dataset.Modality = "CT"
        return dataset
        
    def _validate_modality_specific(self):
        """Test-specific validation."""
        pass


class TestCorruptedDicomReferences:
    """Test handling of corrupted or invalid DICOM reference images."""
    
    def test_missing_required_dicom_elements(self):
        """Test handling of DICOM reference missing required elements."""
        # Create incomplete DICOM dataset
        incomplete_dataset = Dataset()
        incomplete_dataset.PatientID = "TEST001"
        # Missing critical elements like StudyInstanceUID, SeriesInstanceUID
        
        with pytest.raises(DicomCreationError) as excinfo:
            creator = TestBaseDicomCreator(reference_image=incomplete_dataset)
            creator.validate()
        
        error = excinfo.value
        assert "missing" in str(error).lower()
        assert len(error.suggestions) > 0
        assert any("required DICOM elements" in s for s in error.suggestions)
    
    def test_invalid_dicom_uid_format(self):
        """Test handling of invalid UID formats in reference DICOM."""
        dataset = Dataset()
        dataset.PatientID = "TEST001"
        dataset.StudyInstanceUID = "invalid.uid.format"  # Invalid UID
        dataset.SeriesInstanceUID = "1.2.3"  # Too short
        
        with pytest.raises(ValidationError) as excinfo:
            creator = TestBaseDicomCreator(reference_image=dataset)
            creator.validate()
        
        error = excinfo.value
        assert "UID" in str(error)
        assert len(error.suggestions) > 0
    
    def test_corrupted_geometric_information(self):
        """Test handling of corrupted geometric information in reference."""
        dataset = Dataset()
        dataset.PatientID = "TEST001"
        dataset.StudyInstanceUID = "1.2.3.4.5.6.7.8.9"
        dataset.SeriesInstanceUID = "1.2.3.4.5.6.7.8.10"
        
        # Corrupted geometric data
        dataset.ImageOrientationPatient = [1.0, 0.0, 0.0, 0.0, 1.0]  # Missing element
        dataset.ImagePositionPatient = [0.0, 0.0]  # Missing Z coordinate
        dataset.PixelSpacing = [1.0]  # Missing second value
        
        with pytest.raises(ValidationError) as excinfo:
            creator = TestBaseDicomCreator(reference_image=dataset)
            creator.validate()
        
        error = excinfo.value
        assert "geometric" in str(error).lower() or "orientation" in str(error).lower()
        assert len(error.suggestions) > 0
    
    def test_non_dicom_reference_object(self):
        """Test handling of non-DICOM object as reference."""
        invalid_reference = {"not": "a_dicom_dataset"}
        
        with pytest.raises(DicomCreationError) as excinfo:
            creator = TestBaseDicomCreator(reference_image=invalid_reference)
        
        error = excinfo.value
        assert "DICOM" in str(error)
        assert len(error.suggestions) > 0
        assert any("pydicom Dataset" in s for s in error.suggestions)
    
    def test_none_reference_image(self):
        """Test handling of None reference image when required."""
        # Some operations might require a reference image
        creator = TestBaseDicomCreator(reference_image=None)
        
        # Should handle gracefully and provide guidance
        assert creator.reference_image is None
        
        # If validation requires reference, should provide helpful error
        try:
            creator.validate()
        except ValidationError as e:
            assert len(e.suggestions) > 0


class TestIncompletePatientInformation:
    """Test handling of incomplete or invalid patient information."""
    
    def test_completely_empty_patient_info(self):
        """Test handling of completely empty patient information."""
        creator = TestBaseDicomCreator(patient_info={})
        
        with pytest.raises(ValidationError) as excinfo:
            creator.validate()
        
        error = excinfo.value
        assert "PatientID" in str(error)
        assert "required" in str(error).lower()
        assert len(error.suggestions) > 0
        assert any("PatientID" in s for s in error.suggestions)
    
    def test_invalid_patient_id_characters(self):
        """Test handling of invalid characters in PatientID."""
        invalid_patient_ids = [
            "PATIENT@001",  # Invalid @ character
            "PATIENT#001",  # Invalid # character
            "PATIENT/001",  # Invalid / character
            "PATIENT\\001",  # Invalid \ character
            "PATIENT<001>",  # Invalid < > characters
            "",  # Empty string
            "   ",  # Only whitespace
        ]
        
        validator = PatientInfoValidator()
        
        for invalid_id in invalid_patient_ids:
            patient_info = {"PatientID": invalid_id}
            errors = validator.validate_patient_info(patient_info)
            
            assert len(errors) > 0, f"PatientID '{invalid_id}' should be invalid"
            # Check that error message contains helpful guidance
            error_text = " ".join(errors)
            assert "PatientID" in error_text
    
    def test_invalid_date_formats(self):
        """Test handling of invalid date formats."""
        invalid_dates = [
            "2024-01-01",  # Wrong format (should be YYYYMMDD)
            "01/01/2024",  # Wrong format
            "20240132",  # Invalid day
            "20241301",  # Invalid month
            "202401",  # Too short
            "202401011",  # Too long
            "abcd1234",  # Non-numeric
            "",  # Empty
        ]
        
        validator = PatientInfoValidator()
        
        for invalid_date in invalid_dates:
            patient_info = {"PatientID": "TEST", "PatientBirthDate": invalid_date}
            errors = validator.validate_patient_info(patient_info)
            
            assert len(errors) > 0, f"Date '{invalid_date}' should be invalid"
            error_text = " ".join(errors)
            assert "date" in error_text.lower() or "format" in error_text.lower()
    
    def test_invalid_patient_sex_values(self):
        """Test handling of invalid patient sex values."""
        invalid_sex_values = [
            "Male",  # Should be single character
            "Female",  # Should be single character
            "X",  # Invalid character
            "m",  # Wrong case
            "f",  # Wrong case
            "123",  # Numeric
        ]
        
        validator = PatientInfoValidator()
        
        for invalid_sex in invalid_sex_values:
            patient_info = {"PatientID": "TEST", "PatientSex": invalid_sex}
            errors = validator.validate_patient_info(patient_info)
            
            assert len(errors) > 0, f"PatientSex '{invalid_sex}' should be invalid"
    
    def test_invalid_patient_age_formats(self):
        """Test handling of invalid patient age formats."""
        invalid_ages = [
            "25",  # Missing unit
            "25 years",  # Wrong format
            "25YRS",  # Wrong unit
            "999X",  # Invalid unit
            "25.5Y",  # Decimal not allowed
            "-25Y",  # Negative age
            "1000Y",  # Too large
            "",  # Empty
        ]
        
        validator = PatientInfoValidator()
        
        for invalid_age in invalid_ages:
            patient_info = {"PatientID": "TEST", "PatientAge": invalid_age}
            errors = validator.validate_patient_info(patient_info)
            
            assert len(errors) > 0, f"PatientAge '{invalid_age}' should be invalid"
    
    def test_oversized_patient_fields(self):
        """Test handling of oversized patient information fields."""
        oversized_patient = {
            "PatientID": "A" * 100,  # Exceeds 64 character limit
            "PatientName": "A" * 100,  # Exceeds limit
            "PatientBirthDate": "202401011",  # Too long
            "PatientSex": "MALE",  # Too long
            "PatientAge": "25YEARS",  # Too long
        }
        
        validator = PatientInfoValidator()
        errors = validator.validate_patient_info(oversized_patient)
        
        assert len(errors) > 0
        # Should have multiple errors for different oversized fields
        error_text = " ".join(errors)
        assert "length" in error_text.lower() or "exceeds" in error_text.lower()


class TestCoordinateSystemInconsistencies:
    """Test handling of coordinate system inconsistencies."""
    
    def test_invalid_image_orientation_vectors(self):
        """Test handling of invalid image orientation vectors."""
        invalid_orientations = [
            [1.0, 0.0, 0.0, 0.0, 1.0],  # Missing element
            [1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0],  # Too many elements
            [2.0, 0.0, 0.0, 0.0, 1.0, 0.0],  # Not unit vector
            [1.0, 1.0, 0.0, 0.0, 1.0, 0.0],  # Not orthogonal
            [np.nan, 0.0, 0.0, 0.0, 1.0, 0.0],  # Contains NaN
            [np.inf, 0.0, 0.0, 0.0, 1.0, 0.0],  # Contains infinity
        ]
        
        for invalid_orientation in invalid_orientations:
            with pytest.raises((ValidationError, CoordinateSystemError)) as excinfo:
                params = GeometricParameters(
                    origin=np.array([0.0, 0.0, 0.0]),
                    pixel_spacing=np.array([1.0, 1.0]),
                    slice_thickness=2.5,
                    image_orientation=np.array(invalid_orientation),
                    image_position=np.array([0.0, 0.0, 0.0])
                )
                validator = GeometricValidator()
                errors = validator.validate_geometric_parameters(params)
                if errors:
                    raise ValidationError(f"Invalid orientation: {errors}")
            
            error = excinfo.value
            assert len(error.suggestions) > 0 if hasattr(error, 'suggestions') else True
    
    def test_inconsistent_coordinate_frames(self):
        """Test handling of inconsistent coordinate frames."""
        # Create two frames with incompatible parameters
        frame1_params = GeometricParameters(
            origin=np.array([0.0, 0.0, 0.0]),
            pixel_spacing=np.array([1.0, 1.0]),
            slice_thickness=2.5,
            image_orientation=np.array([1.0, 0.0, 0.0, 0.0, 1.0, 0.0]),
            image_position=np.array([0.0, 0.0, 0.0])
        )
        
        frame2_params = GeometricParameters(
            origin=np.array([1000.0, 1000.0, 1000.0]),  # Very different origin
            pixel_spacing=np.array([10.0, 10.0]),  # Very different spacing
            slice_thickness=50.0,  # Very different thickness
            image_orientation=np.array([0.0, 1.0, 0.0, -1.0, 0.0, 0.0]),  # 90° rotation
            image_position=np.array([1000.0, 1000.0, 1000.0])
        )
        
        frame1 = FrameOfReference("frame1", frame1_params)
        frame2 = FrameOfReference("frame2", frame2_params)
        
        transformer = CoordinateTransformer()
        
        # Test coordinates that might cause issues
        problematic_coords = np.array([
            [np.nan, 0.0, 0.0],  # NaN coordinate
            [np.inf, 0.0, 0.0],  # Infinite coordinate
            [1e10, 1e10, 1e10],  # Extremely large coordinates
        ])
        
        with pytest.raises((CoordinateSystemError, ValueError)):
            transformer.transform_coordinates(problematic_coords, frame1, frame2)
    
    def test_invalid_pixel_spacing_values(self):
        """Test handling of invalid pixel spacing values."""
        invalid_spacings = [
            [0.0, 1.0],  # Zero spacing
            [-1.0, 1.0],  # Negative spacing
            [np.nan, 1.0],  # NaN spacing
            [np.inf, 1.0],  # Infinite spacing
            [1.0],  # Missing second value
            [1.0, 1.0, 1.0],  # Too many values
        ]
        
        for invalid_spacing in invalid_spacings:
            with pytest.raises((ValidationError, ValueError)):
                params = GeometricParameters(
                    origin=np.array([0.0, 0.0, 0.0]),
                    pixel_spacing=np.array(invalid_spacing),
                    slice_thickness=2.5,
                    image_orientation=np.array([1.0, 0.0, 0.0, 0.0, 1.0, 0.0]),
                    image_position=np.array([0.0, 0.0, 0.0])
                )
                validator = GeometricValidator()
                errors = validator.validate_geometric_parameters(params)
                if errors:
                    raise ValidationError(f"Invalid spacing: {errors}")
    
    def test_invalid_structure_coordinates(self):
        """Test handling of invalid structure coordinates."""
        invalid_coordinate_sets = [
            np.array([[np.nan, 0.0, 0.0]]),  # NaN coordinates
            np.array([[np.inf, 0.0, 0.0]]),  # Infinite coordinates
            np.array([[1e10, 1e10, 1e10]]),  # Extremely large coordinates
            np.array([]),  # Empty array
            np.array([[1.0, 2.0]]),  # Wrong dimensions (missing Z)
            np.array([[[1.0, 2.0, 3.0]]]),  # Wrong shape (3D instead of 2D)
        ]
        
        for invalid_coords in invalid_coordinate_sets:
            with pytest.raises((ValidationError, ValueError, IndexError)):
                errors = validate_structure_geometry(invalid_coords, "test_structure")
                if errors:
                    raise ValidationError(f"Invalid coordinates: {errors}")


class TestInvalidParameterCombinations:
    """Test handling of invalid parameter combinations."""
    
    def test_incompatible_dicom_tag_combinations(self):
        """Test handling of incompatible DICOM tag value combinations."""
        validator = DicomTagValidator()
        
        # Test invalid VR/value combinations
        invalid_combinations = [
            ("DA", "not_a_date"),  # Date VR with non-date value
            ("DS", "not_a_number"),  # Decimal String VR with non-numeric value
            ("CS", "lowercase_code"),  # Code String VR with lowercase (should be uppercase)
            ("AS", "25 years"),  # Age String VR with wrong format
        ]
        
        for vr, value in invalid_combinations:
            errors = validator.validate_tag_value("TestTag", value, vr)
            assert len(errors) > 0, f"VR {vr} with value '{value}' should be invalid"
    
    def test_creator_with_all_invalid_inputs(self):
        """Test creator with multiple invalid inputs simultaneously."""
        # Combine multiple types of invalid inputs
        invalid_patient = {
            "PatientID": "",  # Empty (invalid)
            "PatientName": "A" * 100,  # Too long
            "PatientBirthDate": "invalid_date",  # Invalid format
            "PatientSex": "Invalid",  # Invalid value
            "PatientAge": "25 years",  # Invalid format
        }
        
        # Create incomplete reference dataset
        invalid_reference = Dataset()
        invalid_reference.StudyInstanceUID = "invalid.uid"  # Invalid UID
        
        creator = TestBaseDicomCreator(
            reference_image=invalid_reference,
            patient_info=invalid_patient
        )
        
        with pytest.raises(ValidationError) as excinfo:
            creator.validate()
        
        error = excinfo.value
        # Should provide comprehensive error information
        assert len(error.suggestions) > 0
        # Error message should mention multiple issues
        error_str = str(error)
        assert len(error_str) > 50  # Should be a substantial error message
    
    def test_recovery_suggestions_quality(self):
        """Test that error recovery suggestions are actionable and helpful."""
        # Test various error scenarios and verify suggestion quality
        test_scenarios = [
            (ValidationError("PatientID is required"), ["PatientID", "provide", "set"]),
            (CoordinateSystemError("Invalid orientation"), ["orientation", "check", "verify"]),
            (DicomCreationError("Missing DICOM elements"), ["elements", "add", "include"]),
        ]
        
        for error, expected_keywords in test_scenarios:
            if hasattr(error, 'suggestions') and error.suggestions:
                suggestions_text = " ".join(error.suggestions).lower()
                # Check that suggestions contain relevant keywords
                keyword_found = any(keyword in suggestions_text for keyword in expected_keywords)
                assert keyword_found, f"Suggestions should contain relevant keywords: {error.suggestions}"
                
                # Check that suggestions are actionable (contain action words)
                action_words = ["check", "verify", "ensure", "set", "provide", "add", "remove", "correct"]
                has_action = any(action in suggestions_text for action in action_words)
                assert has_action, f"Suggestions should be actionable: {error.suggestions}"
